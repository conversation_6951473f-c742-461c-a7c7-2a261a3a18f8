"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/layout",{

/***/ "(app-pages-browser)/./src/app/collection/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/collection/layout.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Database,HardDrive,Server,Settings,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_MenuContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/MenuContent */ \"(app-pages-browser)/./src/app/collection/components/MenuContent.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CollectionSystemLayout(param) {\n    let { children } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"datasources\");\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 使用useEffect来设置客户端时间，避免SSR/客户端不匹配\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setCurrentTime(new Date().toLocaleString());\n        // 可选：每分钟更新一次时间\n        const interval = setInterval(()=>{\n            setCurrentTime(new Date().toLocaleString());\n        }, 60000);\n        return ()=>clearInterval(interval);\n    }, []);\n    // Agent运行情况数据\n    const agentStats = {\n        totalAgents: 24,\n        onlineAgents: 22,\n        offlineAgents: 2,\n        totalDataSources: 156,\n        activeDataSources: 142,\n        totalDataVolume: \"2.8TB\",\n        lastUpdate: currentTime || \"加载中...\"\n    };\n    // Agent详细信息\n    const agentDetails = [\n        {\n            id: 1,\n            name: \"Agent-Gov-001\",\n            department: \"市政府办公厅\",\n            server: \"192.168.1.101\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 3,\n            lastHeartbeat: \"2分钟前\",\n            dataTypes: [\n                \"政务公开数据\",\n                \"会议纪要数据\",\n                \"公文流转数据\"\n            ],\n            deployTime: \"2024-01-15 09:30:00\",\n            deployedBy: \"张三\",\n            deployEnvironment: \"生产环境\"\n        },\n        {\n            id: 2,\n            name: \"Agent-Dev-002\",\n            department: \"发展改革委\",\n            server: \"192.168.1.102\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 4,\n            lastHeartbeat: \"1分钟前\",\n            dataTypes: [\n                \"项目投资数据\",\n                \"经济指标数据\",\n                \"规划数据\",\n                \"价格监测数据\"\n            ],\n            deployTime: \"2024-01-20 14:15:00\",\n            deployedBy: \"李四\",\n            deployEnvironment: \"生产环境\"\n        },\n        {\n            id: 3,\n            name: \"Agent-Edu-003\",\n            department: \"教育局\",\n            server: \"192.168.1.103\",\n            status: \"warning\",\n            version: \"v2.1.2\",\n            dataSources: 5,\n            lastHeartbeat: \"15分钟前\",\n            dataTypes: [\n                \"学生学籍数据\",\n                \"教师信息数据\",\n                \"学校基础数据\",\n                \"考试成绩数据\",\n                \"教育资源数据\"\n            ],\n            deployTime: \"2023-12-28 16:45:00\",\n            deployedBy: \"王五\",\n            deployEnvironment: \"生产环境\"\n        },\n        {\n            id: 4,\n            name: \"Agent-Tech-004\",\n            department: \"科技局\",\n            server: \"192.168.1.104\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 3,\n            lastHeartbeat: \"3分钟前\",\n            dataTypes: [\n                \"科技项目数据\",\n                \"专利申请数据\",\n                \"科技企业数据\"\n            ],\n            deployTime: \"2024-02-01 10:20:00\",\n            deployedBy: \"赵六\",\n            deployEnvironment: \"生产环境\"\n        },\n        {\n            id: 5,\n            name: \"Agent-Ind-005\",\n            department: \"工信局\",\n            server: \"192.168.1.105\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 4,\n            lastHeartbeat: \"1分钟前\",\n            dataTypes: [\n                \"工业企业数据\",\n                \"产业园区数据\",\n                \"信息化项目数据\",\n                \"节能减排数据\"\n            ]\n        },\n        {\n            id: 6,\n            name: \"Agent-Pub-006\",\n            department: \"公安局\",\n            server: \"192.168.1.106\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 6,\n            lastHeartbeat: \"实时\",\n            dataTypes: [\n                \"人口基础信息\",\n                \"户籍管理数据\",\n                \"身份证数据\",\n                \"治安管理数据\",\n                \"交通违法数据\",\n                \"案件管理数据\"\n            ]\n        },\n        {\n            id: 7,\n            name: \"Agent-Civ-007\",\n            department: \"民政局\",\n            server: \"192.168.1.107\",\n            status: \"offline\",\n            version: \"v2.1.1\",\n            dataSources: 4,\n            lastHeartbeat: \"2小时前\",\n            dataTypes: [\n                \"婚姻登记数据\",\n                \"社会救助数据\",\n                \"养老服务数据\",\n                \"社会组织数据\"\n            ]\n        }\n    ];\n    // 导航菜单项\n    const menuItems = [\n        {\n            id: \"datasources\",\n            name: \"数据源连接\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"数据来源单位连接状态\"\n        },\n        {\n            id: \"agents\",\n            name: \"Agent状态\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"采集Agent运行监控\"\n        },\n        {\n            id: \"monitoring\",\n            name: \"实时监控\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"系统运行状态监控\"\n        },\n        {\n            id: \"settings\",\n            name: \"系统配置\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"采集系统参数配置\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 29\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 30\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"offline\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDD04 数据采集系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"24个数据来源单位的数据采集情况实时监控\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-1\",\n                                children: menuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = activeTab === item.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(item.id),\n                                        className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 \".concat(isActive ? \"bg-white/80 text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-purple-600 hover:bg-white/40\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-purple-600 transition-colors text-sm font-medium\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"bg-purple-600 text-white px-4 py-2 rounded-xl hover:bg-purple-700 transition-colors text-sm font-medium\",\n                                        children: \"主控台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/60 backdrop-blur-sm border-b border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-semibold text-gray-900\",\n                                        children: \"系统状态概览\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"Agent在线: \",\n                                                            agentStats.onlineAgents,\n                                                            \"/\",\n                                                            agentStats.totalAgents\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"数据量: \",\n                                                            agentStats.totalDataVolume\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Database_HardDrive_Server_Settings_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"数据源: \",\n                                                            agentStats.activeDataSources,\n                                                            \"/\",\n                                                            agentStats.totalDataSources\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"最后更新: \",\n                                    agentStats.lastUpdate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MenuContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                activeTab: activeTab,\n                agentStats: agentStats,\n                agentDetails: agentDetails,\n                getStatusIcon: getStatusIcon,\n                getStatusColor: getStatusColor\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10\",\n                children: activeTab === \"datasources\" ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"请在上方菜单中选择要查看的内容\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemLayout, \"BSrznD5uGL+XGQbWVacZx64Bo8M=\");\n_c = CollectionSystemLayout;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/layout.tsx\n"));

/***/ })

});