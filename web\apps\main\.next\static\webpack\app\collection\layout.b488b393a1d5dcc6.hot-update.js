"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/layout",{

/***/ "(app-pages-browser)/./src/app/collection/components/MenuContent.tsx":
/*!*******************************************************!*\
  !*** ./src/app/collection/components/MenuContent.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MenuContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _AgentStatus__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AgentStatus */ \"(app-pages-browser)/./src/app/collection/components/AgentStatus.tsx\");\n/* harmony import */ var _RealTimeMonitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealTimeMonitoring */ \"(app-pages-browser)/./src/app/collection/components/RealTimeMonitoring.tsx\");\n/* harmony import */ var _SystemSettings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SystemSettings */ \"(app-pages-browser)/./src/app/collection/components/SystemSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MenuContent(param) {\n    let { activeTab, agentStats, agentDetails, getStatusIcon, getStatusColor } = param;\n    // 当选择数据源时，不显示菜单内容，由 page.tsx 显示\n    if (activeTab === \"datasources\") {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/40 backdrop-blur-sm border-b border-white/20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w mx-auto px-6 py-4\",\n            children: [\n                activeTab === \"agents\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AgentStatus__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    agentStats: agentStats,\n                    agentDetails: agentDetails,\n                    getStatusIcon: getStatusIcon,\n                    getStatusColor: getStatusColor\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"monitoring\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeMonitoring__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 40\n                }, this),\n                activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemSettings__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 38\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_c = MenuContent;\nvar _c;\n$RefreshReg$(_c, \"MenuContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/components/MenuContent.tsx\n"));

/***/ })

});