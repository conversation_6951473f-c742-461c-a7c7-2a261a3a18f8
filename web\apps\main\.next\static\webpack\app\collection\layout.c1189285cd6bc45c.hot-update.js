"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/layout",{

/***/ "(app-pages-browser)/./src/app/collection/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/collection/layout.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CollectionSystemLayout(param) {\n    let { children } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"datasources\");\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 使用useEffect来设置客户端时间，避免SSR/客户端不匹配\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setCurrentTime(new Date().toLocaleString());\n        // 可选：每分钟更新一次时间\n        const interval = setInterval(()=>{\n            setCurrentTime(new Date().toLocaleString());\n        }, 60000);\n        return ()=>clearInterval(interval);\n    }, []);\n    // Agent运行情况数据\n    const agentStats = {\n        totalAgents: 24,\n        onlineAgents: 22,\n        offlineAgents: 2,\n        totalDataSources: 156,\n        activeDataSources: 142,\n        totalDataVolume: \"2.8TB\",\n        lastUpdate: currentTime || \"加载中...\"\n    };\n    // Agent详细信息\n    const agentDetails = [\n        {\n            id: 1,\n            name: \"Agent-Gov-001\",\n            department: \"市政府办公厅\",\n            server: \"192.168.1.101\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 3,\n            lastHeartbeat: \"2分钟前\",\n            dataTypes: [\n                \"政务公开数据\",\n                \"会议纪要数据\",\n                \"公文流转数据\"\n            ]\n        },\n        {\n            id: 2,\n            name: \"Agent-Dev-002\",\n            department: \"发展改革委\",\n            server: \"192.168.1.102\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 4,\n            lastHeartbeat: \"1分钟前\",\n            dataTypes: [\n                \"项目投资数据\",\n                \"经济指标数据\",\n                \"规划数据\",\n                \"价格监测数据\"\n            ]\n        },\n        {\n            id: 3,\n            name: \"Agent-Edu-003\",\n            department: \"教育局\",\n            server: \"192.168.1.103\",\n            status: \"warning\",\n            version: \"v2.1.2\",\n            dataSources: 5,\n            lastHeartbeat: \"15分钟前\",\n            dataTypes: [\n                \"学生学籍数据\",\n                \"教师信息数据\",\n                \"学校基础数据\",\n                \"考试成绩数据\",\n                \"教育资源数据\"\n            ]\n        },\n        {\n            id: 4,\n            name: \"Agent-Tech-004\",\n            department: \"科技局\",\n            server: \"192.168.1.104\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 3,\n            lastHeartbeat: \"3分钟前\",\n            dataTypes: [\n                \"科技项目数据\",\n                \"专利申请数据\",\n                \"科技企业数据\"\n            ]\n        },\n        {\n            id: 5,\n            name: \"Agent-Ind-005\",\n            department: \"工信局\",\n            server: \"192.168.1.105\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 4,\n            lastHeartbeat: \"1分钟前\",\n            dataTypes: [\n                \"工业企业数据\",\n                \"产业园区数据\",\n                \"信息化项目数据\",\n                \"节能减排数据\"\n            ]\n        },\n        {\n            id: 6,\n            name: \"Agent-Pub-006\",\n            department: \"公安局\",\n            server: \"192.168.1.106\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 6,\n            lastHeartbeat: \"实时\",\n            dataTypes: [\n                \"人口基础信息\",\n                \"户籍管理数据\",\n                \"身份证数据\",\n                \"治安管理数据\",\n                \"交通违法数据\",\n                \"案件管理数据\"\n            ]\n        },\n        {\n            id: 7,\n            name: \"Agent-Civ-007\",\n            department: \"民政局\",\n            server: \"192.168.1.107\",\n            status: \"offline\",\n            version: \"v2.1.1\",\n            dataSources: 4,\n            lastHeartbeat: \"2小时前\",\n            dataTypes: [\n                \"婚姻登记数据\",\n                \"社会救助数据\",\n                \"养老服务数据\",\n                \"社会组织数据\"\n            ]\n        }\n    ];\n    // 导航菜单项\n    const menuItems = [\n        {\n            id: \"datasources\",\n            name: \"数据源连接\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: \"数据来源单位连接状态\"\n        },\n        {\n            id: \"agents\",\n            name: \"Agent状态\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"采集Agent运行监控\"\n        },\n        {\n            id: \"monitoring\",\n            name: \"实时监控\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"系统运行状态监控\"\n        },\n        {\n            id: \"settings\",\n            name: \"系统配置\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"采集系统参数配置\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 29\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 30\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"offline\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDD04 数据采集系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"24个数据来源单位的数据采集情况实时监控\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-1\",\n                                children: menuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = activeTab === item.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(item.id),\n                                        className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 \".concat(isActive ? \"bg-white/80 text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-purple-600 hover:bg-white/40\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-purple-600 transition-colors text-sm font-medium\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"bg-purple-600 text-white px-4 py-2 rounded-xl hover:bg-purple-700 transition-colors text-sm font-medium\",\n                                        children: \"主控台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/60 backdrop-blur-sm border-b border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"系统状态概览\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"Agent在线: \",\n                                                            agentStats.onlineAgents,\n                                                            \"/\",\n                                                            agentStats.totalAgents\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"数据量: \",\n                                                            agentStats.totalDataVolume\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"数据源: \",\n                                                            agentStats.activeDataSources,\n                                                            \"/\",\n                                                            agentStats.totalDataSources\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"最后更新: \",\n                                    agentStats.lastUpdate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            activeTab !== \"datasources\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/40 backdrop-blur-sm border-b border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-4\",\n                    children: [\n                        activeTab === \"agents\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Agent运行状态\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"最后更新: \",\n                                                agentStats.lastUpdate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"总Agent数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-gray-900\",\n                                                                children: agentStats.totalAgents\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"在线Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-green-600\",\n                                                                children: agentStats.onlineAgents\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"离线Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-red-600\",\n                                                                children: agentStats.offlineAgents\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"数据量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-purple-600\",\n                                                                children: agentStats.totalDataVolume\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: agentDetails.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20 hover:shadow-md transition-all\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                getStatusIcon(agent.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded text-xs font-medium border \".concat(getStatusColor(agent.status)),\n                                                                    children: agent.status === \"online\" ? \"在线\" : agent.status === \"offline\" ? \"离线\" : \"异常\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: agent.department\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: agent.server\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-3 h-3 text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: [\n                                                                                \"版本: \",\n                                                                                agent.version\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"数据源: \",\n                                                                        agent.dataSources\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"心跳: \",\n                                                                        agent.lastHeartbeat\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mb-1\",\n                                                            children: \"采集数据类型:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: [\n                                                                agent.dataTypes.slice(0, 2).map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs\",\n                                                                        children: type\n                                                                    }, index, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 29\n                                                                    }, this)),\n                                                                agent.dataTypes.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        agent.dataTypes.length - 2\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, agent.id, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 15\n                        }, this),\n                        activeTab === \"monitoring\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"实时监控\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: \"系统负载\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: \"23%\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"CPU使用率\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: \"存储使用\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: \"67%\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"磁盘占用率\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: \"网络流量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: \"1.2GB/s\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"实时传输速率\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 15\n                        }, this),\n                        activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"系统配置\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2\",\n                                                    children: \"采集配置\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"默认采集频率:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"每小时\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"数据保留期:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"365天\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"最大并发连接:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2\",\n                                                    children: \"Agent配置\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"心跳间隔:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"30秒\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"超时时间:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"5分钟\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"重试次数:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"3次\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10\",\n                children: activeTab === \"datasources\" ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"请在上方菜单中选择要查看的内容\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemLayout, \"BSrznD5uGL+XGQbWVacZx64Bo8M=\");\n_c = CollectionSystemLayout;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29sbGVjdGlvbi9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWlCcUI7QUFFc0I7QUFFNUIsU0FBU2MsdUJBQXVCLEtBSTlDO1FBSjhDLEVBQzdDQyxRQUFRLEVBR1QsR0FKOEM7O0lBSzdDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNNLGFBQWFDLGVBQWUsR0FBR1AsK0NBQVFBLENBQUM7SUFFL0MsbUNBQW1DO0lBQ25DQyxnREFBU0EsQ0FBQztRQUNSTSxlQUFlLElBQUlDLE9BQU9DLGNBQWM7UUFFeEMsZUFBZTtRQUNmLE1BQU1DLFdBQVdDLFlBQVk7WUFDM0JKLGVBQWUsSUFBSUMsT0FBT0MsY0FBYztRQUMxQyxHQUFHO1FBRUgsT0FBTyxJQUFNRyxjQUFjRjtJQUM3QixHQUFHLEVBQUU7SUFFTCxjQUFjO0lBQ2QsTUFBTUcsYUFBYTtRQUNqQkMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLGVBQWU7UUFDZkMsa0JBQWtCO1FBQ2xCQyxtQkFBbUI7UUFDbkJDLGlCQUFpQjtRQUNqQkMsWUFBWWQsZUFBZTtJQUM3QjtJQUVBLFlBQVk7SUFDWixNQUFNZSxlQUFlO1FBQ25CO1lBQ0VDLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxZQUFZO1lBQ1pDLFFBQVE7WUFDUkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsZUFBZTtZQUNmQyxXQUFXO2dCQUFDO2dCQUFVO2dCQUFVO2FBQVM7UUFDM0M7UUFDQTtZQUNFUixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZkMsV0FBVztnQkFBQztnQkFBVTtnQkFBVTtnQkFBUTthQUFTO1FBQ25EO1FBQ0E7WUFDRVIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFlBQVk7WUFDWkMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxlQUFlO1lBQ2ZDLFdBQVc7Z0JBQUM7Z0JBQVU7Z0JBQVU7Z0JBQVU7Z0JBQVU7YUFBUztRQUMvRDtRQUNBO1lBQ0VSLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxZQUFZO1lBQ1pDLFFBQVE7WUFDUkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsZUFBZTtZQUNmQyxXQUFXO2dCQUFDO2dCQUFVO2dCQUFVO2FBQVM7UUFDM0M7UUFDQTtZQUNFUixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZkMsV0FBVztnQkFBQztnQkFBVTtnQkFBVTtnQkFBVzthQUFTO1FBQ3REO1FBQ0E7WUFDRVIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFlBQVk7WUFDWkMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxlQUFlO1lBQ2ZDLFdBQVc7Z0JBQUM7Z0JBQVU7Z0JBQVU7Z0JBQVM7Z0JBQVU7Z0JBQVU7YUFBUztRQUN4RTtRQUNBO1lBQ0VSLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxZQUFZO1lBQ1pDLFFBQVE7WUFDUkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsZUFBZTtZQUNmQyxXQUFXO2dCQUFDO2dCQUFVO2dCQUFVO2dCQUFVO2FBQVM7UUFDckQ7S0FDRDtJQUVELFFBQVE7SUFDUixNQUFNQyxZQUFZO1FBQ2hCO1lBQ0VULElBQUk7WUFDSkMsTUFBTTtZQUNOUyxNQUFNNUMscUxBQVFBO1lBQ2Q2QyxhQUFhO1FBQ2Y7UUFDQTtZQUNFWCxJQUFJO1lBQ0pDLE1BQU07WUFDTlMsTUFBTTNDLHFMQUFNQTtZQUNaNEMsYUFBYTtRQUNmO1FBQ0E7WUFDRVgsSUFBSTtZQUNKQyxNQUFNO1lBQ05TLE1BQU0xQyxxTEFBUUE7WUFDZDJDLGFBQWE7UUFDZjtRQUNBO1lBQ0VYLElBQUk7WUFDSkMsTUFBTTtZQUNOUyxNQUFNeEMscUxBQVFBO1lBQ2R5QyxhQUFhO1FBQ2Y7S0FDRDtJQUVELE1BQU1DLGdCQUFnQixDQUFDUjtRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQVUscUJBQU8sOERBQUNqQyxxTEFBV0E7b0JBQUMwQyxXQUFVOzs7Ozs7WUFDN0MsS0FBSztnQkFBVyxxQkFBTyw4REFBQ3pDLHFMQUFXQTtvQkFBQ3lDLFdBQVU7Ozs7OztZQUM5QyxLQUFLO2dCQUFXLHFCQUFPLDhEQUFDeEMscUxBQUtBO29CQUFDd0MsV0FBVTs7Ozs7O1lBQ3hDO2dCQUFTLHFCQUFPLDhEQUFDekMscUxBQVdBO29CQUFDeUMsV0FBVTs7Ozs7O1FBQ3pDO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQ1Y7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFDQSxxQkFDRSw4REFBQ1c7UUFBSUYsV0FBVTs7MEJBRWIsOERBQUNHO2dCQUFPSCxXQUFVOzBCQUNoQiw0RUFBQ0U7b0JBQUlGLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFJRixXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUlGLFdBQVU7MENBRWIsNEVBQUNFO29DQUFJRixXQUFVOztzREFDYiw4REFBQ0U7NENBQUlGLFdBQVU7c0RBQ2IsNEVBQUMvQyxxTEFBUUE7Z0RBQUMrQyxXQUFVOzs7Ozs7Ozs7OztzREFFdEIsOERBQUNFOzs4REFDQyw4REFBQ0U7b0RBQUdKLFdBQVU7OERBQWtHOzs7Ozs7OERBR2hILDhEQUFDSztvREFBRUwsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU0zQyw4REFBQ0U7Z0NBQUlGLFdBQVU7MENBQ1pKLFVBQVVVLEdBQUcsQ0FBQyxDQUFDQztvQ0FDZCxNQUFNQyxPQUFPRCxLQUFLVixJQUFJO29DQUN0QixNQUFNWSxXQUFXeEMsY0FBY3NDLEtBQUtwQixFQUFFO29DQUN0QyxxQkFDRSw4REFBQ3VCO3dDQUVDQyxTQUFTLElBQU16QyxhQUFhcUMsS0FBS3BCLEVBQUU7d0NBQ25DYSxXQUFXLGdGQUlWLE9BSENTLFdBQ0ksMENBQ0E7OzBEQUdOLDhEQUFDRDtnREFBS1IsV0FBVTs7Ozs7OzBEQUNoQiw4REFBQ1k7Z0RBQUtaLFdBQVU7MERBQXVCTyxLQUFLbkIsSUFBSTs7Ozs7Ozt1Q0FUM0NtQixLQUFLcEIsRUFBRTs7Ozs7Z0NBWWxCOzs7Ozs7MENBSUYsOERBQUNlO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ2E7d0NBQ0NDLE1BQUs7d0NBQ0xkLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ2E7d0NBQ0NDLE1BQUs7d0NBQ0xkLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1QsOERBQUNFO2dCQUFJRixXQUFVOzBCQUNiLDRFQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0U7d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUNlO3dDQUFHZixXQUFVO2tEQUFzQzs7Ozs7O2tEQUNwRCw4REFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDRTtnREFBSUYsV0FBVTs7a0VBQ2IsOERBQUNFO3dEQUFJRixXQUFVOzs7Ozs7a0VBQ2YsOERBQUNZO3dEQUFLWixXQUFVOzs0REFBZ0I7NERBQVV0QixXQUFXRSxZQUFZOzREQUFDOzREQUFFRixXQUFXQyxXQUFXOzs7Ozs7Ozs7Ozs7OzBEQUU1Riw4REFBQ3VCO2dEQUFJRixXQUFVOztrRUFDYiw4REFBQ3RDLHFMQUFTQTt3REFBQ3NDLFdBQVU7Ozs7OztrRUFDckIsOERBQUNZO3dEQUFLWixXQUFVOzs0REFBZ0I7NERBQU10QixXQUFXTSxlQUFlOzs7Ozs7Ozs7Ozs7OzBEQUVsRSw4REFBQ2tCO2dEQUFJRixXQUFVOztrRUFDYiw4REFBQ3JDLHNMQUFJQTt3REFBQ3FDLFdBQVU7Ozs7OztrRUFDaEIsOERBQUNZO3dEQUFLWixXQUFVOzs0REFBZ0I7NERBQU10QixXQUFXSyxpQkFBaUI7NERBQUM7NERBQUVMLFdBQVdJLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdEcsOERBQUNvQjtnQ0FBSUYsV0FBVTs7b0NBQXdCO29DQUM5QnRCLFdBQVdPLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT25DaEIsY0FBYywrQkFDYiw4REFBQ2lDO2dCQUFJRixXQUFVOzBCQUNiLDRFQUFDRTtvQkFBSUYsV0FBVTs7d0JBQ1ovQixjQUFjLDBCQUNiLDhEQUFDaUM7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNlOzRDQUFHZixXQUFVO3NEQUFzQzs7Ozs7O3NEQUNwRCw4REFBQ0U7NENBQUlGLFdBQVU7O2dEQUF3QjtnREFDOUJ0QixXQUFXTyxVQUFVOzs7Ozs7Ozs7Ozs7OzhDQUtoQyw4REFBQ2lCO29DQUFJRixXQUFVOztzREFDYiw4REFBQ0U7NENBQUlGLFdBQVU7c0RBQ2IsNEVBQUNFO2dEQUFJRixXQUFVOztrRUFDYiw4REFBQzlDLHFMQUFNQTt3REFBQzhDLFdBQVU7Ozs7OztrRUFDbEIsOERBQUNFOzswRUFDQyw4REFBQ0c7Z0VBQUVMLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDSztnRUFBRUwsV0FBVTswRUFBbUN0QixXQUFXQyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJNUUsOERBQUN1Qjs0Q0FBSUYsV0FBVTtzREFDYiw0RUFBQ0U7Z0RBQUlGLFdBQVU7O2tFQUNiLDhEQUFDMUMscUxBQVdBO3dEQUFDMEMsV0FBVTs7Ozs7O2tFQUN2Qiw4REFBQ0U7OzBFQUNDLDhEQUFDRztnRUFBRUwsV0FBVTswRUFBd0I7Ozs7OzswRUFDckMsOERBQUNLO2dFQUFFTCxXQUFVOzBFQUFvQ3RCLFdBQVdFLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUk5RSw4REFBQ3NCOzRDQUFJRixXQUFVO3NEQUNiLDRFQUFDRTtnREFBSUYsV0FBVTs7a0VBQ2IsOERBQUN6QyxxTEFBV0E7d0RBQUN5QyxXQUFVOzs7Ozs7a0VBQ3ZCLDhEQUFDRTs7MEVBQ0MsOERBQUNHO2dFQUFFTCxXQUFVOzBFQUF3Qjs7Ozs7OzBFQUNyQyw4REFBQ0s7Z0VBQUVMLFdBQVU7MEVBQWtDdEIsV0FBV0csYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSTdFLDhEQUFDcUI7NENBQUlGLFdBQVU7c0RBQ2IsNEVBQUNFO2dEQUFJRixXQUFVOztrRUFDYiw4REFBQ3BDLHNMQUFTQTt3REFBQ29DLFdBQVU7Ozs7OztrRUFDckIsOERBQUNFOzswRUFDQyw4REFBQ0c7Z0VBQUVMLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDSztnRUFBRUwsV0FBVTswRUFBcUN0QixXQUFXTSxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPcEYsOERBQUNrQjtvQ0FBSUYsV0FBVTs4Q0FDWmQsYUFBYW9CLEdBQUcsQ0FBQyxDQUFDVSxzQkFDakIsOERBQUNkOzRDQUFtQkYsV0FBVTs7OERBQzVCLDhEQUFDRTtvREFBSUYsV0FBVTs7c0VBQ2IsOERBQUNFOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQzlDLHFMQUFNQTtvRUFBQzhDLFdBQVU7Ozs7Ozs4RUFDbEIsOERBQUNpQjtvRUFBR2pCLFdBQVU7OEVBQStCZ0IsTUFBTTVCLElBQUk7Ozs7Ozs7Ozs7OztzRUFFekQsOERBQUNjOzREQUFJRixXQUFVOztnRUFDWkQsY0FBY2lCLE1BQU16QixNQUFNOzhFQUMzQiw4REFBQ3FCO29FQUFLWixXQUFXLGdEQUE2RSxPQUE3QkMsZUFBZWUsTUFBTXpCLE1BQU07OEVBQ3pGeUIsTUFBTXpCLE1BQU0sS0FBSyxXQUFXLE9BQU95QixNQUFNekIsTUFBTSxLQUFLLFlBQVksT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUs5RSw4REFBQ1c7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBSUYsV0FBVTs7OEVBQ2IsOERBQUN2QyxzTEFBU0E7b0VBQUN1QyxXQUFVOzs7Ozs7OEVBQ3JCLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBaUJnQixNQUFNM0IsVUFBVTs7Ozs7Ozs7Ozs7O3NFQUVuRCw4REFBQ2E7NERBQUlGLFdBQVU7OzhFQUNiLDhEQUFDdEMscUxBQVNBO29FQUFDc0MsV0FBVTs7Ozs7OzhFQUNyQiw4REFBQ1k7b0VBQUtaLFdBQVU7OEVBQWlCZ0IsTUFBTTFCLE1BQU07Ozs7Ozs7Ozs7OztzRUFFL0MsOERBQUNZOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ0U7b0VBQUlGLFdBQVU7O3NGQUNiLDhEQUFDNUMsc0xBQU1BOzRFQUFDNEMsV0FBVTs7Ozs7O3NGQUNsQiw4REFBQ1k7NEVBQUtaLFdBQVU7O2dGQUFnQjtnRkFBS2dCLE1BQU14QixPQUFPOzs7Ozs7Ozs7Ozs7OzhFQUVwRCw4REFBQ29CO29FQUFLWixXQUFVOzt3RUFBZ0I7d0VBQU1nQixNQUFNdkIsV0FBVzs7Ozs7Ozs7Ozs7OztzRUFFekQsOERBQUNTOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ3hDLHFMQUFLQTtvRUFBQ3dDLFdBQVU7Ozs7Ozs4RUFDakIsOERBQUNZO29FQUFLWixXQUFVOzt3RUFBZ0I7d0VBQUtnQixNQUFNdEIsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJNUQsOERBQUNRO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ0s7NERBQUVMLFdBQVU7c0VBQTZCOzs7Ozs7c0VBQzFDLDhEQUFDRTs0REFBSUYsV0FBVTs7Z0VBQ1pnQixNQUFNckIsU0FBUyxDQUFDdUIsS0FBSyxDQUFDLEdBQUcsR0FBR1osR0FBRyxDQUFDLENBQUNhLE1BQU1DLHNCQUN0Qyw4REFBQ1I7d0VBQWlCWixXQUFVO2tGQUN6Qm1CO3VFQURRQzs7Ozs7Z0VBSVpKLE1BQU1yQixTQUFTLENBQUMwQixNQUFNLEdBQUcsbUJBQ3hCLDhEQUFDVDtvRUFBS1osV0FBVTs7d0VBQXNEO3dFQUNsRWdCLE1BQU1yQixTQUFTLENBQUMwQixNQUFNLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQTlDM0JMLE1BQU03QixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dCQXlEekJsQixjQUFjLDhCQUNiLDhEQUFDaUM7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDZTtvQ0FBR2YsV0FBVTs4Q0FBc0M7Ozs7Ozs4Q0FDcEQsOERBQUNFO29DQUFJRixXQUFVOztzREFDYiw4REFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDRTtvREFBSUYsV0FBVTs7c0VBQ2IsOERBQUM3QyxxTEFBUUE7NERBQUM2QyxXQUFVOzs7Ozs7c0VBQ3BCLDhEQUFDaUI7NERBQUdqQixXQUFVO3NFQUE4Qjs7Ozs7Ozs7Ozs7OzhEQUU5Qyw4REFBQ0s7b0RBQUVMLFdBQVU7OERBQW9DOzs7Ozs7OERBQ2pELDhEQUFDSztvREFBRUwsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7OztzREFFdkMsOERBQUNFOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ0U7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDdEMscUxBQVNBOzREQUFDc0MsV0FBVTs7Ozs7O3NFQUNyQiw4REFBQ2lCOzREQUFHakIsV0FBVTtzRUFBOEI7Ozs7Ozs7Ozs7Ozs4REFFOUMsOERBQUNLO29EQUFFTCxXQUFVOzhEQUFtQzs7Ozs7OzhEQUNoRCw4REFBQ0s7b0RBQUVMLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7c0RBRXZDLDhEQUFDRTs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ3JDLHNMQUFJQTs0REFBQ3FDLFdBQVU7Ozs7OztzRUFDaEIsOERBQUNpQjs0REFBR2pCLFdBQVU7c0VBQThCOzs7Ozs7Ozs7Ozs7OERBRTlDLDhEQUFDSztvREFBRUwsV0FBVTs4REFBcUM7Ozs7Ozs4REFDbEQsOERBQUNLO29EQUFFTCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU01Qy9CLGNBQWMsNEJBQ2IsOERBQUNpQzs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNlO29DQUFHZixXQUFVOzhDQUFzQzs7Ozs7OzhDQUNwRCw4REFBQ0U7b0NBQUlGLFdBQVU7O3NEQUNiLDhEQUFDRTs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNpQjtvREFBR2pCLFdBQVU7OERBQW1DOzs7Ozs7OERBQ2pELDhEQUFDRTtvREFBSUYsV0FBVTs7c0VBQ2IsOERBQUNFOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ1k7b0VBQUtaLFdBQVU7OEVBQWdCOzs7Ozs7OEVBQ2hDLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBZ0I7Ozs7Ozs7Ozs7OztzRUFFbEMsOERBQUNFOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ1k7b0VBQUtaLFdBQVU7OEVBQWdCOzs7Ozs7OEVBQ2hDLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBZ0I7Ozs7Ozs7Ozs7OztzRUFFbEMsOERBQUNFOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ1k7b0VBQUtaLFdBQVU7OEVBQWdCOzs7Ozs7OEVBQ2hDLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJdEMsOERBQUNFOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ2lCO29EQUFHakIsV0FBVTs4REFBbUM7Ozs7Ozs4REFDakQsOERBQUNFO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ0U7NERBQUlGLFdBQVU7OzhFQUNiLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNZO29FQUFLWixXQUFVOzhFQUFnQjs7Ozs7Ozs7Ozs7O3NFQUVsQyw4REFBQ0U7NERBQUlGLFdBQVU7OzhFQUNiLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNZO29FQUFLWixXQUFVOzhFQUFnQjs7Ozs7Ozs7Ozs7O3NFQUVsQyw4REFBQ0U7NERBQUlGLFdBQVU7OzhFQUNiLDhEQUFDWTtvRUFBS1osV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNZO29FQUFLWixXQUFVOzhFQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBWWxELDhEQUFDc0I7Z0JBQUt0QixXQUFVOzBCQUNiL0IsY0FBYyxnQkFBZ0JELHlCQUM3Qiw4REFBQ2tDO29CQUFJRixXQUFVOzhCQUNiLDRFQUFDRTt3QkFBSUYsV0FBVTtrQ0FDYiw0RUFBQ0s7c0NBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPWCw4REFBQ0g7Z0JBQUlGLFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBSUYsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRTt3QkFBSUYsV0FBVTt3QkFBa0l1QixPQUFPOzRCQUFFQyxnQkFBZ0I7d0JBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUl2TDtHQXZjd0J6RDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbGxlY3Rpb24vbGF5b3V0LnRzeD9lOTJkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQge1xuICBEYXRhYmFzZSxcbiAgU2VydmVyLFxuICBBY3Rpdml0eSxcbiAgU2hpZWxkLFxuICBTZXR0aW5ncyxcbiAgQ2hlY2tDaXJjbGUsXG4gIEFsZXJ0Q2lyY2xlLFxuICBDbG9jayxcbiAgQnVpbGRpbmcyLFxuICBIYXJkRHJpdmUsXG4gIFdpZmksXG4gIERvd25sb2FkLFxuICBFeWUsXG4gIEJhckNoYXJ0M1xufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvbGxlY3Rpb25TeXN0ZW1MYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ2RhdGFzb3VyY2VzJylcbiAgY29uc3QgW2N1cnJlbnRUaW1lLCBzZXRDdXJyZW50VGltZV0gPSB1c2VTdGF0ZSgnJylcblxuICAvLyDkvb/nlKh1c2VFZmZlY3TmnaXorr7nva7lrqLmiLfnq6/ml7bpl7TvvIzpgb/lhY1TU1Iv5a6i5oi356uv5LiN5Yy56YWNXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0Q3VycmVudFRpbWUobmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpKVxuXG4gICAgLy8g5Y+v6YCJ77ya5q+P5YiG6ZKf5pu05paw5LiA5qyh5pe26Ze0XG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXRDdXJyZW50VGltZShuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCkpXG4gICAgfSwgNjAwMDApXG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbClcbiAgfSwgW10pXG5cbiAgLy8gQWdlbnTov5DooYzmg4XlhrXmlbDmja5cbiAgY29uc3QgYWdlbnRTdGF0cyA9IHtcbiAgICB0b3RhbEFnZW50czogMjQsXG4gICAgb25saW5lQWdlbnRzOiAyMixcbiAgICBvZmZsaW5lQWdlbnRzOiAyLFxuICAgIHRvdGFsRGF0YVNvdXJjZXM6IDE1NixcbiAgICBhY3RpdmVEYXRhU291cmNlczogMTQyLFxuICAgIHRvdGFsRGF0YVZvbHVtZTogJzIuOFRCJyxcbiAgICBsYXN0VXBkYXRlOiBjdXJyZW50VGltZSB8fCAn5Yqg6L295LitLi4uJ1xuICB9XG5cbiAgLy8gQWdlbnTor6bnu4bkv6Hmga9cbiAgY29uc3QgYWdlbnREZXRhaWxzID0gW1xuICAgIHtcbiAgICAgIGlkOiAxLFxuICAgICAgbmFtZTogJ0FnZW50LUdvdi0wMDEnLFxuICAgICAgZGVwYXJ0bWVudDogJ+W4guaUv+W6nOWKnuWFrOWOhScsXG4gICAgICBzZXJ2ZXI6ICcxOTIuMTY4LjEuMTAxJyxcbiAgICAgIHN0YXR1czogJ29ubGluZScsXG4gICAgICB2ZXJzaW9uOiAndjIuMS4zJyxcbiAgICAgIGRhdGFTb3VyY2VzOiAzLFxuICAgICAgbGFzdEhlYXJ0YmVhdDogJzLliIbpkp/liY0nLFxuICAgICAgZGF0YVR5cGVzOiBbJ+aUv+WKoeWFrOW8gOaVsOaNricsICfkvJrorq7nuqropoHmlbDmja4nLCAn5YWs5paH5rWB6L2s5pWw5o2uJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAyLFxuICAgICAgbmFtZTogJ0FnZW50LURldi0wMDInLFxuICAgICAgZGVwYXJ0bWVudDogJ+WPkeWxleaUuemdqeWnlCcsXG4gICAgICBzZXJ2ZXI6ICcxOTIuMTY4LjEuMTAyJyxcbiAgICAgIHN0YXR1czogJ29ubGluZScsXG4gICAgICB2ZXJzaW9uOiAndjIuMS4zJyxcbiAgICAgIGRhdGFTb3VyY2VzOiA0LFxuICAgICAgbGFzdEhlYXJ0YmVhdDogJzHliIbpkp/liY0nLFxuICAgICAgZGF0YVR5cGVzOiBbJ+mhueebruaKlei1hOaVsOaNricsICfnu4/mtY7mjIfmoIfmlbDmja4nLCAn6KeE5YiS5pWw5o2uJywgJ+S7t+agvOebkea1i+aVsOaNriddXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogMyxcbiAgICAgIG5hbWU6ICdBZ2VudC1FZHUtMDAzJyxcbiAgICAgIGRlcGFydG1lbnQ6ICfmlZnogrLlsYAnLFxuICAgICAgc2VydmVyOiAnMTkyLjE2OC4xLjEwMycsXG4gICAgICBzdGF0dXM6ICd3YXJuaW5nJyxcbiAgICAgIHZlcnNpb246ICd2Mi4xLjInLFxuICAgICAgZGF0YVNvdXJjZXM6IDUsXG4gICAgICBsYXN0SGVhcnRiZWF0OiAnMTXliIbpkp/liY0nLFxuICAgICAgZGF0YVR5cGVzOiBbJ+WtpueUn+WtpuexjeaVsOaNricsICfmlZnluIjkv6Hmga/mlbDmja4nLCAn5a2m5qCh5Z+656GA5pWw5o2uJywgJ+iAg+ivleaIkOe7qeaVsOaNricsICfmlZnogrLotYTmupDmlbDmja4nXVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDQsXG4gICAgICBuYW1lOiAnQWdlbnQtVGVjaC0wMDQnLFxuICAgICAgZGVwYXJ0bWVudDogJ+enkeaKgOWxgCcsXG4gICAgICBzZXJ2ZXI6ICcxOTIuMTY4LjEuMTA0JyxcbiAgICAgIHN0YXR1czogJ29ubGluZScsXG4gICAgICB2ZXJzaW9uOiAndjIuMS4zJyxcbiAgICAgIGRhdGFTb3VyY2VzOiAzLFxuICAgICAgbGFzdEhlYXJ0YmVhdDogJzPliIbpkp/liY0nLFxuICAgICAgZGF0YVR5cGVzOiBbJ+enkeaKgOmhueebruaVsOaNricsICfkuJPliKnnlLPor7fmlbDmja4nLCAn56eR5oqA5LyB5Lia5pWw5o2uJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA1LFxuICAgICAgbmFtZTogJ0FnZW50LUluZC0wMDUnLFxuICAgICAgZGVwYXJ0bWVudDogJ+W3peS/oeWxgCcsXG4gICAgICBzZXJ2ZXI6ICcxOTIuMTY4LjEuMTA1JyxcbiAgICAgIHN0YXR1czogJ29ubGluZScsXG4gICAgICB2ZXJzaW9uOiAndjIuMS4zJyxcbiAgICAgIGRhdGFTb3VyY2VzOiA0LFxuICAgICAgbGFzdEhlYXJ0YmVhdDogJzHliIbpkp/liY0nLFxuICAgICAgZGF0YVR5cGVzOiBbJ+W3peS4muS8geS4muaVsOaNricsICfkuqfkuJrlm63ljLrmlbDmja4nLCAn5L+h5oGv5YyW6aG555uu5pWw5o2uJywgJ+iKguiDveWHj+aOkuaVsOaNriddXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogNixcbiAgICAgIG5hbWU6ICdBZ2VudC1QdWItMDA2JyxcbiAgICAgIGRlcGFydG1lbnQ6ICflhazlronlsYAnLFxuICAgICAgc2VydmVyOiAnMTkyLjE2OC4xLjEwNicsXG4gICAgICBzdGF0dXM6ICdvbmxpbmUnLFxuICAgICAgdmVyc2lvbjogJ3YyLjEuMycsXG4gICAgICBkYXRhU291cmNlczogNixcbiAgICAgIGxhc3RIZWFydGJlYXQ6ICflrp7ml7YnLFxuICAgICAgZGF0YVR5cGVzOiBbJ+S6uuWPo+WfuuehgOS/oeaBrycsICfmiLfnsY3nrqHnkIbmlbDmja4nLCAn6Lqr5Lu96K+B5pWw5o2uJywgJ+ayu+WuieeuoeeQhuaVsOaNricsICfkuqTpgJrov53ms5XmlbDmja4nLCAn5qGI5Lu2566h55CG5pWw5o2uJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA3LFxuICAgICAgbmFtZTogJ0FnZW50LUNpdi0wMDcnLFxuICAgICAgZGVwYXJ0bWVudDogJ+awkeaUv+WxgCcsXG4gICAgICBzZXJ2ZXI6ICcxOTIuMTY4LjEuMTA3JyxcbiAgICAgIHN0YXR1czogJ29mZmxpbmUnLFxuICAgICAgdmVyc2lvbjogJ3YyLjEuMScsXG4gICAgICBkYXRhU291cmNlczogNCxcbiAgICAgIGxhc3RIZWFydGJlYXQ6ICcy5bCP5pe25YmNJyxcbiAgICAgIGRhdGFUeXBlczogWyflqZrlp7vnmbvorrDmlbDmja4nLCAn56S+5Lya5pWR5Yqp5pWw5o2uJywgJ+WFu+iAgeacjeWKoeaVsOaNricsICfnpL7kvJrnu4Tnu4fmlbDmja4nXVxuICAgIH1cbiAgXVxuXG4gIC8vIOWvvOiIquiPnOWNlemhuVxuICBjb25zdCBtZW51SXRlbXMgPSBbXG4gICAge1xuICAgICAgaWQ6ICdkYXRhc291cmNlcycsXG4gICAgICBuYW1lOiAn5pWw5o2u5rqQ6L+e5o6lJyxcbiAgICAgIGljb246IERhdGFiYXNlLFxuICAgICAgZGVzY3JpcHRpb246ICfmlbDmja7mnaXmupDljZXkvY3ov57mjqXnirbmgIEnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2FnZW50cycsXG4gICAgICBuYW1lOiAnQWdlbnTnirbmgIEnLFxuICAgICAgaWNvbjogU2VydmVyLFxuICAgICAgZGVzY3JpcHRpb246ICfph4fpm4ZBZ2VudOi/kOihjOebkeaOpydcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnbW9uaXRvcmluZycsXG4gICAgICBuYW1lOiAn5a6e5pe255uR5o6nJyxcbiAgICAgIGljb246IEFjdGl2aXR5LFxuICAgICAgZGVzY3JpcHRpb246ICfns7vnu5/ov5DooYznirbmgIHnm5HmjqcnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3NldHRpbmdzJyxcbiAgICAgIG5hbWU6ICfns7vnu5/phY3nva4nLFxuICAgICAgaWNvbjogU2V0dGluZ3MsXG4gICAgICBkZXNjcmlwdGlvbjogJ+mHh+mbhuezu+e7n+WPguaVsOmFjee9ridcbiAgICB9XG4gIF1cblxuICBjb25zdCBnZXRTdGF0dXNJY29uID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ29ubGluZSc6IHJldHVybiA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICBjYXNlICdvZmZsaW5lJzogcmV0dXJuIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcmVkLTUwMFwiIC8+XG4gICAgICBjYXNlICd3YXJuaW5nJzogcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQteWVsbG93LTUwMFwiIC8+XG4gICAgICBkZWZhdWx0OiByZXR1cm4gPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnb25saW5lJzogcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAgYm9yZGVyLWdyZWVuLTIwMCdcbiAgICAgIGNhc2UgJ29mZmxpbmUnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNzAwIGJvcmRlci1yZWQtMjAwJ1xuICAgICAgY2FzZSAnd2FybmluZyc6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy03MDAgYm9yZGVyLXllbGxvdy0yMDAnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgYm9yZGVyLWdyYXktMjAwJ1xuICAgIH1cbiAgfVxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTUwIHZpYS12aW9sZXQtNTAgdG8tcHVycGxlLTEwMFwiPlxuICAgICAgey8qIOezu+e7n+WktOmDqCAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci14bCBib3JkZXItYiBib3JkZXItd2hpdGUvMjAgc3RpY2t5IHRvcC0wIHotNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdyBteC1hdXRvIHB4LTYgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICB7Lyog57O757ufTG9nbyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS01MDAgdG8tdmlvbGV0LTUwMCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxEYXRhYmFzZSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLXZpb2xldC02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgICAg8J+UhCDmlbDmja7ph4fpm4bns7vnu59cbiAgICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj4yNOS4quaVsOaNruadpea6kOWNleS9jeeahOaVsOaNrumHh+mbhuaDheWGteWunuaXtuebkeaOpzwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOWvvOiIquiPnOWNlSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICB7bWVudUl0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IEljb24gPSBpdGVtLmljb25cbiAgICAgICAgICAgICAgICBjb25zdCBpc0FjdGl2ZSA9IGFjdGl2ZVRhYiA9PT0gaXRlbS5pZFxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5pZH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKGl0ZW0uaWQpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtMyBweS0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlLzgwIHRleHQtcHVycGxlLTYwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtcHVycGxlLTYwMCBob3ZlcjpiZy13aGl0ZS80MCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDov5Tlm57kuLvlubPlj7AgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgIGhyZWY9XCIvXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtcHVycGxlLTYwMCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOi/lOWbnummlumhtVxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmRcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC14bCBob3ZlcjpiZy1wdXJwbGUtNzAwIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5Li75o6n5Y+wXG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvaGVhZGVyPlxuXG4gICAgICB7Lyog57O757uf54q25oCB5qaC6KeIICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS82MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci1iIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13IG14LWF1dG8gcHgtNiBweS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC02XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPuezu+e7n+eKtuaAgeamguiniDwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTUwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkFnZW505Zyo57q/OiB7YWdlbnRTdGF0cy5vbmxpbmVBZ2VudHN9L3thZ2VudFN0YXRzLnRvdGFsQWdlbnRzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPEhhcmREcml2ZSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPuaVsOaNrumHjzoge2FnZW50U3RhdHMudG90YWxEYXRhVm9sdW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPFdpZmkgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+5pWw5o2u5rqQOiB7YWdlbnRTdGF0cy5hY3RpdmVEYXRhU291cmNlc30ve2FnZW50U3RhdHMudG90YWxEYXRhU291cmNlc308L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICDmnIDlkI7mm7TmlrA6IHthZ2VudFN0YXRzLmxhc3RVcGRhdGV9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOiPnOWNleWGheWuueWMuuWfnyAqL31cbiAgICAgIHthY3RpdmVUYWIgIT09ICdkYXRhc291cmNlcycgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzQwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyLWIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdyBteC1hdXRvIHB4LTYgcHktNFwiPlxuICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2FnZW50cycgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5BZ2VudOi/kOihjOeKtuaAgTwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICDmnIDlkI7mm7TmlrA6IHthZ2VudFN0YXRzLmxhc3RVcGRhdGV9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBBZ2VudOe7n+iuoeWNoeeJhyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlcnZlciBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7mgLtBZ2VudOaVsDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57YWdlbnRTdGF0cy50b3RhbEFnZW50c308L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIHJvdW5kZWQtbGcgcC00IGJvcmRlciBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPuWcqOe6v0FnZW50PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57YWdlbnRTdGF0cy5vbmxpbmVBZ2VudHN9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+56a757q/QWdlbnQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXJlZC02MDBcIj57YWdlbnRTdGF0cy5vZmZsaW5lQWdlbnRzfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgcm91bmRlZC1sZyBwLTQgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXB1cnBsZS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7mlbDmja7ph488L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj57YWdlbnRTdGF0cy50b3RhbERhdGFWb2x1bWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIEFnZW506K+m57uG5YiX6KGoICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAge2FnZW50RGV0YWlscy5tYXAoKGFnZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXthZ2VudC5pZH0gY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgcm91bmRlZC1sZyBwLTQgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VydmVyIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj57YWdlbnQubmFtZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzSWNvbihhZ2VudC5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJvcmRlciAke2dldFN0YXR1c0NvbG9yKGFnZW50LnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2FnZW50LnN0YXR1cyA9PT0gJ29ubGluZScgPyAn5Zyo57q/JyA6IGFnZW50LnN0YXR1cyA9PT0gJ29mZmxpbmUnID8gJ+emu+e6vycgOiAn5byC5bi4J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cInctMyBoLTMgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57YWdlbnQuZGVwYXJ0bWVudH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxIYXJkRHJpdmUgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPnthZ2VudC5zZXJ2ZXJ9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+54mI5pysOiB7YWdlbnQudmVyc2lvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+5pWw5o2u5rqQOiB7YWdlbnQuZGF0YVNvdXJjZXN9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPuW/g+i3szoge2FnZW50Lmxhc3RIZWFydGJlYXR9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTMgcHQtMyBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtYi0xXCI+6YeH6ZuG5pWw5o2u57G75Z6LOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2FnZW50LmRhdGFUeXBlcy5zbGljZSgwLCAyKS5tYXAoKHR5cGUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwicHgtMiBweS0xIGJnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtNzAwIHJvdW5kZWQgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3R5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2FnZW50LmRhdGFUeXBlcy5sZW5ndGggPiAyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0yIHB5LTEgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMCByb3VuZGVkIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICt7YWdlbnQuZGF0YVR5cGVzLmxlbmd0aCAtIDJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ21vbml0b3JpbmcnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj7lrp7ml7bnm5Hmjqc8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPuezu+e7n+i0n+i9vTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj4yMyU8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkNQVeS9v+eUqOeOhzwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8SGFyZERyaXZlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPuWtmOWCqOS9v+eUqDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPjY3JTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+56OB55uY5Y2g55So546HPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIHJvdW5kZWQtbGcgcC00IGJvcmRlciBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxXaWZpIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1wdXJwbGUtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+572R57uc5rWB6YePPC9oND5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj4xLjJHQi9zPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7lrp7ml7bkvKDovpPpgJ/njoc8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnc2V0dGluZ3MnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj7ns7vnu5/phY3nva48L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPumHh+mbhumFjee9rjwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+6buY6K6k6YeH6ZuG6aKR546HOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDBcIj7mr4/lsI/ml7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPuaVsOaNruS/neeVmeacnzo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+MzY15aSpPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7mnIDlpKflubblj5Hov57mjqU6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPjUwPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPkFnZW506YWN572uPC9oND5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7lv4Pot7Ppl7TpmpQ6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPjMw56eSPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7otoXml7bml7bpl7Q6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPjXliIbpkp88L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPumHjeivleasoeaVsDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+M+asoTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDkuLvopoHlhoXlrrnljLrln58gKi99XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdkYXRhc291cmNlcycgPyBjaGlsZHJlbiA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxwPuivt+WcqOS4iuaWueiPnOWNleS4remAieaLqeimgeafpeeci+eahOWGheWuuTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9tYWluPlxuXG4gICAgICB7Lyog6IOM5pmv6KOF6aWwICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHBvaW50ZXItZXZlbnRzLW5vbmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC00MCAtcmlnaHQtNDAgdy04MCBoLTgwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNDAwLzIwIHRvLXZpb2xldC00MDAvMjAgcm91bmRlZC1mdWxsIGJsdXItM3hsIGFuaW1hdGUtZmxvYXRcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTQwIC1sZWZ0LTQwIHctODAgaC04MCBiZy1ncmFkaWVudC10by1yIGZyb20tdmlvbGV0LTQwMC8yMCB0by1wdXJwbGUtNDAwLzIwIHJvdW5kZWQtZnVsbCBibHVyLTN4bCBhbmltYXRlLWZsb2F0XCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcycycgfX0+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkRhdGFiYXNlIiwiU2VydmVyIiwiQWN0aXZpdHkiLCJTaGllbGQiLCJTZXR0aW5ncyIsIkNoZWNrQ2lyY2xlIiwiQWxlcnRDaXJjbGUiLCJDbG9jayIsIkJ1aWxkaW5nMiIsIkhhcmREcml2ZSIsIldpZmkiLCJCYXJDaGFydDMiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNvbGxlY3Rpb25TeXN0ZW1MYXlvdXQiLCJjaGlsZHJlbiIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsImN1cnJlbnRUaW1lIiwic2V0Q3VycmVudFRpbWUiLCJEYXRlIiwidG9Mb2NhbGVTdHJpbmciLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImFnZW50U3RhdHMiLCJ0b3RhbEFnZW50cyIsIm9ubGluZUFnZW50cyIsIm9mZmxpbmVBZ2VudHMiLCJ0b3RhbERhdGFTb3VyY2VzIiwiYWN0aXZlRGF0YVNvdXJjZXMiLCJ0b3RhbERhdGFWb2x1bWUiLCJsYXN0VXBkYXRlIiwiYWdlbnREZXRhaWxzIiwiaWQiLCJuYW1lIiwiZGVwYXJ0bWVudCIsInNlcnZlciIsInN0YXR1cyIsInZlcnNpb24iLCJkYXRhU291cmNlcyIsImxhc3RIZWFydGJlYXQiLCJkYXRhVHlwZXMiLCJtZW51SXRlbXMiLCJpY29uIiwiZGVzY3JpcHRpb24iLCJnZXRTdGF0dXNJY29uIiwiY2xhc3NOYW1lIiwiZ2V0U3RhdHVzQ29sb3IiLCJkaXYiLCJoZWFkZXIiLCJoMSIsInAiLCJtYXAiLCJpdGVtIiwiSWNvbiIsImlzQWN0aXZlIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iLCJhIiwiaHJlZiIsImgzIiwiYWdlbnQiLCJoNCIsInNsaWNlIiwidHlwZSIsImluZGV4IiwibGVuZ3RoIiwibWFpbiIsInN0eWxlIiwiYW5pbWF0aW9uRGVsYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/layout.tsx\n"));

/***/ })

});