"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/layout",{

/***/ "(app-pages-browser)/./src/app/collection/components/MenuContent.tsx":
/*!*******************************************************!*\
  !*** ./src/app/collection/components/MenuContent.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MenuContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _AgentStatus__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AgentStatus */ \"(app-pages-browser)/./src/app/collection/components/AgentStatus.tsx\");\n/* harmony import */ var _RealTimeMonitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealTimeMonitoring */ \"(app-pages-browser)/./src/app/collection/components/RealTimeMonitoring.tsx\");\n/* harmony import */ var _SystemSettings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SystemSettings */ \"(app-pages-browser)/./src/app/collection/components/SystemSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MenuContent(param) {\n    let { activeTab, agentStats, agentDetails, getStatusIcon, getStatusColor } = param;\n    if (activeTab === \"datasources\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white/40 backdrop-blur-sm border-b border-white/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w mx-auto px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DataSourceConnection, {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/40 backdrop-blur-sm border-b border-white/20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w mx-auto px-6 py-4\",\n            children: [\n                activeTab === \"agents\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AgentStatus__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    agentStats: agentStats,\n                    agentDetails: agentDetails,\n                    getStatusIcon: getStatusIcon,\n                    getStatusColor: getStatusColor\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"monitoring\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealTimeMonitoring__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 40\n                }, this),\n                activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemSettings__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 38\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\components\\\\MenuContent.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_c = MenuContent;\nvar _c;\n$RefreshReg$(_c, \"MenuContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/components/MenuContent.tsx\n"));

/***/ })

});