"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/layout",{

/***/ "(app-pages-browser)/./src/app/collection/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/collection/layout.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Building2,CheckCircle,Clock,Database,HardDrive,Server,Settings,Shield,Wifi!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CollectionSystemLayout(param) {\n    let { children } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"datasources\");\n    // Agent运行情况数据\n    const agentStats = {\n        totalAgents: 24,\n        onlineAgents: 22,\n        offlineAgents: 2,\n        totalDataSources: 156,\n        activeDataSources: 142,\n        totalDataVolume: \"2.8TB\",\n        lastUpdate: new Date().toLocaleString()\n    };\n    // Agent详细信息\n    const agentDetails = [\n        {\n            id: 1,\n            name: \"Agent-Gov-001\",\n            department: \"市政府办公厅\",\n            server: \"192.168.1.101\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 3,\n            lastHeartbeat: \"2分钟前\",\n            dataTypes: [\n                \"政务公开数据\",\n                \"会议纪要数据\",\n                \"公文流转数据\"\n            ]\n        },\n        {\n            id: 2,\n            name: \"Agent-Dev-002\",\n            department: \"发展改革委\",\n            server: \"192.168.1.102\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 4,\n            lastHeartbeat: \"1分钟前\",\n            dataTypes: [\n                \"项目投资数据\",\n                \"经济指标数据\",\n                \"规划数据\",\n                \"价格监测数据\"\n            ]\n        },\n        {\n            id: 3,\n            name: \"Agent-Edu-003\",\n            department: \"教育局\",\n            server: \"192.168.1.103\",\n            status: \"warning\",\n            version: \"v2.1.2\",\n            dataSources: 5,\n            lastHeartbeat: \"15分钟前\",\n            dataTypes: [\n                \"学生学籍数据\",\n                \"教师信息数据\",\n                \"学校基础数据\",\n                \"考试成绩数据\",\n                \"教育资源数据\"\n            ]\n        },\n        {\n            id: 4,\n            name: \"Agent-Tech-004\",\n            department: \"科技局\",\n            server: \"192.168.1.104\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 3,\n            lastHeartbeat: \"3分钟前\",\n            dataTypes: [\n                \"科技项目数据\",\n                \"专利申请数据\",\n                \"科技企业数据\"\n            ]\n        },\n        {\n            id: 5,\n            name: \"Agent-Ind-005\",\n            department: \"工信局\",\n            server: \"192.168.1.105\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 4,\n            lastHeartbeat: \"1分钟前\",\n            dataTypes: [\n                \"工业企业数据\",\n                \"产业园区数据\",\n                \"信息化项目数据\",\n                \"节能减排数据\"\n            ]\n        },\n        {\n            id: 6,\n            name: \"Agent-Pub-006\",\n            department: \"公安局\",\n            server: \"192.168.1.106\",\n            status: \"online\",\n            version: \"v2.1.3\",\n            dataSources: 6,\n            lastHeartbeat: \"实时\",\n            dataTypes: [\n                \"人口基础信息\",\n                \"户籍管理数据\",\n                \"身份证数据\",\n                \"治安管理数据\",\n                \"交通违法数据\",\n                \"案件管理数据\"\n            ]\n        },\n        {\n            id: 7,\n            name: \"Agent-Civ-007\",\n            department: \"民政局\",\n            server: \"192.168.1.107\",\n            status: \"offline\",\n            version: \"v2.1.1\",\n            dataSources: 4,\n            lastHeartbeat: \"2小时前\",\n            dataTypes: [\n                \"婚姻登记数据\",\n                \"社会救助数据\",\n                \"养老服务数据\",\n                \"社会组织数据\"\n            ]\n        }\n    ];\n    // 导航菜单项\n    const menuItems = [\n        {\n            id: \"datasources\",\n            name: \"数据源连接\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: \"数据来源单位连接状态\"\n        },\n        {\n            id: \"agents\",\n            name: \"Agent状态\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"采集Agent运行监控\"\n        },\n        {\n            id: \"monitoring\",\n            name: \"实时监控\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"系统运行状态监控\"\n        },\n        {\n            id: \"settings\",\n            name: \"系统配置\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"采集系统参数配置\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 29\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 30\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"offline\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDD04 数据采集系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"24个数据来源单位的数据采集情况实时监控\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-1\",\n                                children: menuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = activeTab === item.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(item.id),\n                                        className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 \".concat(isActive ? \"bg-white/80 text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-purple-600 hover:bg-white/40\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-sm\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-purple-600 transition-colors text-sm font-medium\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"bg-purple-600 text-white px-4 py-2 rounded-xl hover:bg-purple-700 transition-colors text-sm font-medium\",\n                                        children: \"主控台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white/60 backdrop-blur-sm border-b border-white/20 sticky top-[88px] z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: menuItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = activeTab === item.id;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(item.id),\n                                className: \"flex items-center space-x-2 px-4 py-3 rounded-t-lg transition-all duration-200 \".concat(isActive ? \"bg-white/80 text-purple-600 border-b-2 border-purple-600 shadow-sm\" : \"text-gray-600 hover:text-purple-600 hover:bg-white/40\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-sm\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            activeTab !== \"datasources\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/40 backdrop-blur-sm border-b border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w mx-auto px-6 py-4\",\n                    children: [\n                        activeTab === \"agents\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Agent运行状态\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"最后更新: \",\n                                                agentStats.lastUpdate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"总Agent数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-gray-900\",\n                                                                children: agentStats.totalAgents\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"在线Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-green-600\",\n                                                                children: agentStats.onlineAgents\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"离线Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-red-600\",\n                                                                children: agentStats.offlineAgents\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5 text-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"数据量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-purple-600\",\n                                                                children: agentStats.totalDataVolume\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: agentDetails.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20 hover:shadow-md transition-all\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                getStatusIcon(agent.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded text-xs font-medium border \".concat(getStatusColor(agent.status)),\n                                                                    children: agent.status === \"online\" ? \"在线\" : agent.status === \"offline\" ? \"离线\" : \"异常\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: agent.department\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: agent.server\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-3 h-3 text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: [\n                                                                                \"版本: \",\n                                                                                agent.version\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"数据源: \",\n                                                                        agent.dataSources\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"心跳: \",\n                                                                        agent.lastHeartbeat\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mb-1\",\n                                                            children: \"采集数据类型:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: [\n                                                                agent.dataTypes.slice(0, 2).map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs\",\n                                                                        children: type\n                                                                    }, index, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 29\n                                                                    }, this)),\n                                                                agent.dataTypes.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        agent.dataTypes.length - 2\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, agent.id, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 15\n                        }, this),\n                        activeTab === \"monitoring\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"实时监控\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: \"系统负载\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: \"23%\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"CPU使用率\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: \"存储使用\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: \"67%\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"磁盘占用率\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Building2_CheckCircle_Clock_Database_HardDrive_Server_Settings_Shield_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: \"网络流量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: \"1.2GB/s\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"实时传输速率\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 15\n                        }, this),\n                        activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"系统配置\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2\",\n                                                    children: \"采集配置\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"默认采集频率:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"每小时\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"数据保留期:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"365天\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"最大并发连接:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/80 rounded-lg p-4 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2\",\n                                                    children: \"Agent配置\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"心跳间隔:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"30秒\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"超时时间:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"5分钟\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"重试次数:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"3次\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10\",\n                children: activeTab === \"datasources\" ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"请在上方菜单中选择要查看的内容\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\layout.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemLayout, \"3JidQQhDhDQmZbfWAJB9L+6LASk=\");\n_c = CollectionSystemLayout;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/layout.tsx\n"));

/***/ })

});