'use client'

import {
  Server,
  CheckCircle,
  AlertCircle,
  Clock,
  Building2,
  HardDrive,
  Shield,
  BarChart3,
  User,
  Calendar
} from 'lucide-react'

interface AgentStats {
  totalAgents: number
  onlineAgents: number
  offlineAgents: number
  totalDataSources: number
  activeDataSources: number
  totalDataVolume: string
  lastUpdate: string
}

interface AgentDetail {
  id: number
  name: string
  department: string
  server: string
  status: string
  version: string
  dataSources: number
  lastHeartbeat: string
  dataTypes: string[]
  deployTime: string
  deployedBy: string
  deployEnvironment?: string
}

interface AgentStatusProps {
  agentStats: AgentStats
  agentDetails: AgentDetail[]
  getStatusIcon: (status: string) => JSX.Element
  getStatusColor: (status: string) => string
}

export default function AgentStatus({
  agentStats,
  agentDetails,
  getStatusIcon,
  getStatusColor
}: AgentStatusProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Agent运行状态</h3>
        <div className="text-sm text-gray-600">
          最后更新: {agentStats.lastUpdate}
        </div>
      </div>

      {/* Agent统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2">
            <Server className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600">总Agent数</p>
              <p className="text-xl font-bold text-gray-900">{agentStats.totalAgents}</p>
            </div>
          </div>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <div>
              <p className="text-sm text-gray-600">在线Agent</p>
              <p className="text-xl font-bold text-green-600">{agentStats.onlineAgents}</p>
            </div>
          </div>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <div>
              <p className="text-sm text-gray-600">离线Agent</p>
              <p className="text-xl font-bold text-red-600">{agentStats.offlineAgents}</p>
            </div>
          </div>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-purple-500" />
            <div>
              <p className="text-sm text-gray-600">数据量</p>
              <p className="text-xl font-bold text-purple-600">{agentStats.totalDataVolume}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Agent详细列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        {agentDetails.map((agent) => (
          <div key={agent.id} className="bg-white/80 rounded-lg p-3 border border-white/20 hover:shadow-md transition-all">
            {/* 头部信息 */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-1">
                <Server className="w-3 h-3 text-gray-600" />
                <h4 className="font-semibold text-gray-900 text-sm truncate">{agent.name}</h4>
              </div>
              <div className="flex items-center space-x-1">
                {getStatusIcon(agent.status)}
                <span className={`px-1.5 py-0.5 rounded text-xs font-medium border ${getStatusColor(agent.status)}`}>
                  {agent.status === 'online' ? '在线' : agent.status === 'offline' ? '离线' : '异常'}
                </span>
              </div>
            </div>

            {/* 基本信息 */}
            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Building2 className="w-2.5 h-2.5 text-gray-500" />
                  <span className="text-gray-600 truncate">{agent.department}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <HardDrive className="w-2.5 h-2.5 text-gray-500" />
                  <span className="text-gray-600">{agent.server}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Shield className="w-2.5 h-2.5 text-gray-500" />
                  <span className="text-gray-600">{agent.version}</span>
                </div>
                <span className="text-gray-600">源:{agent.dataSources}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="w-2.5 h-2.5 text-gray-500" />
                <span className="text-gray-600">心跳: {agent.lastHeartbeat}</span>
              </div>
            </div>

            {/* 部署信息 */}
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="space-y-1 text-xs">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-2.5 h-2.5 text-gray-500" />
                    <span className="text-gray-600 truncate">{agent.deployTime}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <User className="w-2.5 h-2.5 text-gray-500" />
                    <span className="text-gray-600">{agent.deployedBy}</span>
                  </div>
                  {agent.deployEnvironment && (
                    <span className="text-gray-600 text-xs">{agent.deployEnvironment}</span>
                  )}
                </div>
              </div>
            </div>

            {/* 数据类型 */}
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="flex flex-wrap gap-1">
                {agent.dataTypes.slice(0, 1).map((type, index) => (
                  <span key={index} className="px-1.5 py-0.5 bg-purple-100 text-purple-700 rounded text-xs truncate">
                    {type}
                  </span>
                ))}
                {agent.dataTypes.length > 1 && (
                  <span className="px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded text-xs">
                    +{agent.dataTypes.length - 1}
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
