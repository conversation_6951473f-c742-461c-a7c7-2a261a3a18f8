'use client'

import {
  Database,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'

interface DataSourceConnectionProps {
  // 这里可以添加数据源连接相关的props
  // 目前保持为空，因为原始代码中数据源连接显示的是children内容
}

export default function DataSourceConnection({}: DataSourceConnectionProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">数据源连接状态</h3>
        <div className="text-sm text-gray-600">
          实时监控各数据来源单位连接状态
        </div>
      </div>

      {/* 数据源连接概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2 mb-2">
            <Database className="w-5 h-5 text-blue-500" />
            <h4 className="font-semibold text-gray-900">总连接数</h4>
          </div>
          <p className="text-2xl font-bold text-blue-600">156</p>
          <p className="text-sm text-gray-600">数据源总数</p>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <h4 className="font-semibold text-gray-900">活跃连接</h4>
          </div>
          <p className="text-2xl font-bold text-green-600">142</p>
          <p className="text-sm text-gray-600">正常工作中</p>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2 mb-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <h4 className="font-semibold text-gray-900">异常连接</h4>
          </div>
          <p className="text-2xl font-bold text-red-600">14</p>
          <p className="text-sm text-gray-600">需要处理</p>
        </div>
      </div>

      {/* 连接状态详情 */}
      <div className="bg-white/80 rounded-lg p-4 border border-white/20">
        <h4 className="font-semibold text-gray-900 mb-3">连接状态详情</h4>
        <div className="text-center text-gray-600 py-8">
          <Database className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p>数据源连接详细信息将在此处显示</p>
          <p className="text-sm mt-1">请查看具体的数据源页面获取详细连接状态</p>
        </div>
      </div>
    </div>
  )
}
