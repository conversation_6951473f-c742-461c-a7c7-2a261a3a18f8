'use client'

import DataSourceConnection from './DataSourceConnection'
import AgentStatus from './AgentStatus'
import RealTimeMonitoring from './RealTimeMonitoring'
import SystemSettings from './SystemSettings'

interface AgentStats {
  totalAgents: number
  onlineAgents: number
  offlineAgents: number
  totalDataSources: number
  activeDataSources: number
  totalDataVolume: string
  lastUpdate: string
}

interface AgentDetail {
  id: number
  name: string
  department: string
  server: string
  status: string
  version: string
  dataSources: number
  lastHeartbeat: string
  dataTypes: string[]
}

interface MenuContentProps {
  activeTab: string
  agentStats: AgentStats
  agentDetails: AgentDetail[]
  getStatusIcon: (status: string) => JSX.Element
  getStatusColor: (status: string) => string
}

export default function MenuContent({
  activeTab,
  agentStats,
  agentDetails,
  getStatusIcon,
  getStatusColor
}: MenuContentProps) {
  if (activeTab === 'datasources') {
    return (
      <div className="bg-white/40 backdrop-blur-sm border-b border-white/20">
        <div className="max-w mx-auto px-6 py-4">
          <DataSourceConnection />
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/40 backdrop-blur-sm border-b border-white/20">
      <div className="max-w mx-auto px-6 py-4">
        {activeTab === 'agents' && (
          <AgentStatus
            agentStats={agentStats}
            agentDetails={agentDetails}
            getStatusIcon={getStatusIcon}
            getStatusColor={getStatusColor}
          />
        )}

        {activeTab === 'monitoring' && <RealTimeMonitoring />}

        {activeTab === 'settings' && <SystemSettings />}
      </div>
    </div>
  )
}
