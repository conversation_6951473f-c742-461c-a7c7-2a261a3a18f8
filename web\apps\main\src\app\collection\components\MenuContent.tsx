'use client'

import {
  Server,
  Activity,
  Shield,
  CheckCircle,
  AlertCircle,
  Clock,
  Building2,
  HardDrive,
  Wifi,
  BarChart3
} from 'lucide-react'

interface AgentStats {
  totalAgents: number
  onlineAgents: number
  offlineAgents: number
  totalDataSources: number
  activeDataSources: number
  totalDataVolume: string
  lastUpdate: string
}

interface AgentDetail {
  id: number
  name: string
  department: string
  server: string
  status: string
  version: string
  dataSources: number
  lastHeartbeat: string
  dataTypes: string[]
}

interface MenuContentProps {
  activeTab: string
  agentStats: AgentStats
  agentDetails: AgentDetail[]
  getStatusIcon: (status: string) => JSX.Element
  getStatusColor: (status: string) => string
}

export default function MenuContent({
  activeTab,
  agentStats,
  agentDetails,
  getStatusIcon,
  getStatusColor
}: MenuContentProps) {
  if (activeTab === 'datasources') {
    return null
  }

  return (
    <div className="bg-white/40 backdrop-blur-sm border-b border-white/20">
      <div className="max-w mx-auto px-6 py-4">
        {activeTab === 'agents' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Agent运行状态</h3>
              <div className="text-sm text-gray-600">
                最后更新: {agentStats.lastUpdate}
              </div>
            </div>

            {/* Agent统计卡片 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2">
                  <Server className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">总Agent数</p>
                    <p className="text-xl font-bold text-gray-900">{agentStats.totalAgents}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">在线Agent</p>
                    <p className="text-xl font-bold text-green-600">{agentStats.onlineAgents}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <div>
                    <p className="text-sm text-gray-600">离线Agent</p>
                    <p className="text-xl font-bold text-red-600">{agentStats.offlineAgents}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">数据量</p>
                    <p className="text-xl font-bold text-purple-600">{agentStats.totalDataVolume}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Agent详细列表 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agentDetails.map((agent) => (
                <div key={agent.id} className="bg-white/80 rounded-lg p-4 border border-white/20 hover:shadow-md transition-all">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Server className="w-4 h-4 text-gray-600" />
                      <h4 className="font-semibold text-gray-900">{agent.name}</h4>
                    </div>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(agent.status)}
                      <span className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(agent.status)}`}>
                        {agent.status === 'online' ? '在线' : agent.status === 'offline' ? '离线' : '异常'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Building2 className="w-3 h-3 text-gray-500" />
                      <span className="text-gray-600">{agent.department}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <HardDrive className="w-3 h-3 text-gray-500" />
                      <span className="text-gray-600">{agent.server}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Shield className="w-3 h-3 text-gray-500" />
                        <span className="text-gray-600">版本: {agent.version}</span>
                      </div>
                      <span className="text-gray-600">数据源: {agent.dataSources}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-3 h-3 text-gray-500" />
                      <span className="text-gray-600">心跳: {agent.lastHeartbeat}</span>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-500 mb-1">采集数据类型:</p>
                    <div className="flex flex-wrap gap-1">
                      {agent.dataTypes.slice(0, 2).map((type, index) => (
                        <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                          {type}
                        </span>
                      ))}
                      {agent.dataTypes.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                          +{agent.dataTypes.length - 2}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'monitoring' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">实时监控</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2 mb-2">
                  <Activity className="w-5 h-5 text-green-500" />
                  <h4 className="font-semibold text-gray-900">系统负载</h4>
                </div>
                <p className="text-2xl font-bold text-green-600">23%</p>
                <p className="text-sm text-gray-600">CPU使用率</p>
              </div>
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2 mb-2">
                  <HardDrive className="w-5 h-5 text-blue-500" />
                  <h4 className="font-semibold text-gray-900">存储使用</h4>
                </div>
                <p className="text-2xl font-bold text-blue-600">67%</p>
                <p className="text-sm text-gray-600">磁盘占用率</p>
              </div>
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <div className="flex items-center space-x-2 mb-2">
                  <Wifi className="w-5 h-5 text-purple-500" />
                  <h4 className="font-semibold text-gray-900">网络流量</h4>
                </div>
                <p className="text-2xl font-bold text-purple-600">1.2GB/s</p>
                <p className="text-sm text-gray-600">实时传输速率</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">系统配置</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <h4 className="font-semibold text-gray-900 mb-2">采集配置</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">默认采集频率:</span>
                    <span className="text-gray-900">每小时</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">数据保留期:</span>
                    <span className="text-gray-900">365天</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">最大并发连接:</span>
                    <span className="text-gray-900">50</span>
                  </div>
                </div>
              </div>
              <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                <h4 className="font-semibold text-gray-900 mb-2">Agent配置</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">心跳间隔:</span>
                    <span className="text-gray-900">30秒</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">超时时间:</span>
                    <span className="text-gray-900">5分钟</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">重试次数:</span>
                    <span className="text-gray-900">3次</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
