'use client'

import {
  Database,
  Server,
  Activity,
  Shield,
  Settings,
  CheckCircle,
  AlertCircle,
  Clock,
  Building2,
  HardDrive,
  Wifi,
  Download,
  Eye,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
import { useState, useEffect } from 'react'

export default function CollectionSystemLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [activeTab, setActiveTab] = useState('datasources')
  const [currentTime, setCurrentTime] = useState('')

  // 使用useEffect来设置客户端时间，避免SSR/客户端不匹配
  useEffect(() => {
    setCurrentTime(new Date().toLocaleString())

    // 可选：每分钟更新一次时间
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString())
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  // Agent运行情况数据
  const agentStats = {
    totalAgents: 24,
    onlineAgents: 22,
    offlineAgents: 2,
    totalDataSources: 156,
    activeDataSources: 142,
    totalDataVolume: '2.8TB',
    lastUpdate: currentTime || '加载中...'
  }

  // Agent详细信息
  const agentDetails = [
    {
      id: 1,
      name: 'Agent-Gov-001',
      department: '市政府办公厅',
      server: '192.168.1.101',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 3,
      lastHeartbeat: '2分钟前',
      dataTypes: ['政务公开数据', '会议纪要数据', '公文流转数据']
    },
    {
      id: 2,
      name: 'Agent-Dev-002',
      department: '发展改革委',
      server: '192.168.1.102',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 4,
      lastHeartbeat: '1分钟前',
      dataTypes: ['项目投资数据', '经济指标数据', '规划数据', '价格监测数据']
    },
    {
      id: 3,
      name: 'Agent-Edu-003',
      department: '教育局',
      server: '192.168.1.103',
      status: 'warning',
      version: 'v2.1.2',
      dataSources: 5,
      lastHeartbeat: '15分钟前',
      dataTypes: ['学生学籍数据', '教师信息数据', '学校基础数据', '考试成绩数据', '教育资源数据']
    },
    {
      id: 4,
      name: 'Agent-Tech-004',
      department: '科技局',
      server: '192.168.1.104',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 3,
      lastHeartbeat: '3分钟前',
      dataTypes: ['科技项目数据', '专利申请数据', '科技企业数据']
    },
    {
      id: 5,
      name: 'Agent-Ind-005',
      department: '工信局',
      server: '192.168.1.105',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 4,
      lastHeartbeat: '1分钟前',
      dataTypes: ['工业企业数据', '产业园区数据', '信息化项目数据', '节能减排数据']
    },
    {
      id: 6,
      name: 'Agent-Pub-006',
      department: '公安局',
      server: '192.168.1.106',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 6,
      lastHeartbeat: '实时',
      dataTypes: ['人口基础信息', '户籍管理数据', '身份证数据', '治安管理数据', '交通违法数据', '案件管理数据']
    },
    {
      id: 7,
      name: 'Agent-Civ-007',
      department: '民政局',
      server: '*************',
      status: 'offline',
      version: 'v2.1.1',
      dataSources: 4,
      lastHeartbeat: '2小时前',
      dataTypes: ['婚姻登记数据', '社会救助数据', '养老服务数据', '社会组织数据']
    }
  ]

  // 导航菜单项
  const menuItems = [
    {
      id: 'datasources',
      name: '数据源连接',
      icon: Database,
      description: '数据来源单位连接状态'
    },
    {
      id: 'agents',
      name: 'Agent状态',
      icon: Server,
      description: '采集Agent运行监控'
    },
    {
      id: 'monitoring',
      name: '实时监控',
      icon: Activity,
      description: '系统运行状态监控'
    },
    {
      id: 'settings',
      name: '系统配置',
      icon: Settings,
      description: '采集系统参数配置'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'offline': return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'warning': return <Clock className="w-4 h-4 text-yellow-500" />
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-700 border-green-200'
      case 'offline': return 'bg-red-100 text-red-700 border-red-200'
      case 'warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100">
      {/* 系统头部 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
        <div className="max-w mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* 系统Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <Database className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent">
                    🔄 数据采集系统
                  </h1>
                  <p className="text-sm text-gray-600">24个数据来源单位的数据采集情况实时监控</p>
                </div>
              </div>
            </div>

            {/* 导航菜单 */}
            <div className="hidden lg:flex items-center space-x-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = activeTab === item.id
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-white/80 text-purple-600 shadow-sm'
                        : 'text-gray-600 hover:text-purple-600 hover:bg-white/40'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="font-medium text-sm">{item.name}</span>
                  </button>
                )
              })}
            </div>

            {/* 返回主平台 */}
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-gray-600 hover:text-purple-600 transition-colors text-sm font-medium"
              >
                返回首页
              </a>
              <a
                href="/dashboard"
                className="bg-purple-600 text-white px-4 py-2 rounded-xl hover:bg-purple-700 transition-colors text-sm font-medium"
              >
                主控台
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 系统状态概览 */}
      <div className="bg-white/60 backdrop-blur-sm border-b border-white/20">
        <div className="max-w mx-auto px-6 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <h3 className="text-sm font-semibold text-gray-900">系统状态概览</h3>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-gray-600">Agent在线: {agentStats.onlineAgents}/{agentStats.totalAgents}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <HardDrive className="w-4 h-4 text-blue-500" />
                  <span className="text-gray-600">数据量: {agentStats.totalDataVolume}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Wifi className="w-4 h-4 text-green-500" />
                  <span className="text-gray-600">数据源: {agentStats.activeDataSources}/{agentStats.totalDataSources}</span>
                </div>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              最后更新: {agentStats.lastUpdate}
            </div>
          </div>
        </div>
      </div>

      {/* 菜单内容区域 */}
      {activeTab !== 'datasources' && (
        <div className="bg-white/40 backdrop-blur-sm border-b border-white/20">
          <div className="max-w mx-auto px-6 py-4">
            {activeTab === 'agents' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Agent运行状态</h3>
                  <div className="text-sm text-gray-600">
                    最后更新: {agentStats.lastUpdate}
                  </div>
                </div>

                {/* Agent统计卡片 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2">
                      <Server className="w-5 h-5 text-blue-500" />
                      <div>
                        <p className="text-sm text-gray-600">总Agent数</p>
                        <p className="text-xl font-bold text-gray-900">{agentStats.totalAgents}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <div>
                        <p className="text-sm text-gray-600">在线Agent</p>
                        <p className="text-xl font-bold text-green-600">{agentStats.onlineAgents}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                      <div>
                        <p className="text-sm text-gray-600">离线Agent</p>
                        <p className="text-xl font-bold text-red-600">{agentStats.offlineAgents}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5 text-purple-500" />
                      <div>
                        <p className="text-sm text-gray-600">数据量</p>
                        <p className="text-xl font-bold text-purple-600">{agentStats.totalDataVolume}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Agent详细列表 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agentDetails.map((agent) => (
                    <div key={agent.id} className="bg-white/80 rounded-lg p-4 border border-white/20 hover:shadow-md transition-all">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Server className="w-4 h-4 text-gray-600" />
                          <h4 className="font-semibold text-gray-900">{agent.name}</h4>
                        </div>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(agent.status)}
                          <span className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(agent.status)}`}>
                            {agent.status === 'online' ? '在线' : agent.status === 'offline' ? '离线' : '异常'}
                          </span>
                        </div>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center space-x-2">
                          <Building2 className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-600">{agent.department}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <HardDrive className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-600">{agent.server}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Shield className="w-3 h-3 text-gray-500" />
                            <span className="text-gray-600">版本: {agent.version}</span>
                          </div>
                          <span className="text-gray-600">数据源: {agent.dataSources}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-600">心跳: {agent.lastHeartbeat}</span>
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="text-xs text-gray-500 mb-1">采集数据类型:</p>
                        <div className="flex flex-wrap gap-1">
                          {agent.dataTypes.slice(0, 2).map((type, index) => (
                            <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                              {type}
                            </span>
                          ))}
                          {agent.dataTypes.length > 2 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                              +{agent.dataTypes.length - 2}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'monitoring' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">实时监控</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <Activity className="w-5 h-5 text-green-500" />
                      <h4 className="font-semibold text-gray-900">系统负载</h4>
                    </div>
                    <p className="text-2xl font-bold text-green-600">23%</p>
                    <p className="text-sm text-gray-600">CPU使用率</p>
                  </div>
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <HardDrive className="w-5 h-5 text-blue-500" />
                      <h4 className="font-semibold text-gray-900">存储使用</h4>
                    </div>
                    <p className="text-2xl font-bold text-blue-600">67%</p>
                    <p className="text-sm text-gray-600">磁盘占用率</p>
                  </div>
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <Wifi className="w-5 h-5 text-purple-500" />
                      <h4 className="font-semibold text-gray-900">网络流量</h4>
                    </div>
                    <p className="text-2xl font-bold text-purple-600">1.2GB/s</p>
                    <p className="text-sm text-gray-600">实时传输速率</p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">系统配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <h4 className="font-semibold text-gray-900 mb-2">采集配置</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">默认采集频率:</span>
                        <span className="text-gray-900">每小时</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">数据保留期:</span>
                        <span className="text-gray-900">365天</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">最大并发连接:</span>
                        <span className="text-gray-900">50</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white/80 rounded-lg p-4 border border-white/20">
                    <h4 className="font-semibold text-gray-900 mb-2">Agent配置</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">心跳间隔:</span>
                        <span className="text-gray-900">30秒</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">超时时间:</span>
                        <span className="text-gray-900">5分钟</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">重试次数:</span>
                        <span className="text-gray-900">3次</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <main className="relative z-10">
        {activeTab === 'datasources' ? children : (
          <div className="px-6 py-8">
            <div className="text-center text-gray-600">
              <p>请在上方菜单中选择要查看的内容</p>
            </div>
          </div>
        )}
      </main>

      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
    </div>
  )
}
