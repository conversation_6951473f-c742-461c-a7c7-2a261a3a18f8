'use client'

import {
  Database,
  Server,
  Activity,
  Settings,
  CheckCircle,
  AlertCircle,
  Clock,
  HardDrive,
  Wifi
} from 'lucide-react'
import { useState, useEffect } from 'react'
import MenuContent from './components/MenuContent'

export default function CollectionSystemLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [activeTab, setActiveTab] = useState('datasources')
  const [currentTime, setCurrentTime] = useState('')

  // 使用useEffect来设置客户端时间，避免SSR/客户端不匹配
  useEffect(() => {
    setCurrentTime(new Date().toLocaleString())

    // 可选：每分钟更新一次时间
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString())
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  // Agent运行情况数据
  const agentStats = {
    totalAgents: 24,
    onlineAgents: 22,
    offlineAgents: 2,
    totalDataSources: 156,
    activeDataSources: 142,
    totalDataVolume: '2.8TB',
    lastUpdate: currentTime || '加载中...'
  }

  // Agent详细信息
  const agentDetails = [
    {
      id: 1,
      name: 'Agent-Gov-001',
      department: '市政府办公厅',
      server: '192.168.1.101',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 3,
      lastHeartbeat: '2分钟前',
      dataTypes: ['政务公开数据', '会议纪要数据', '公文流转数据'],
      deployTime: '2024-01-15 09:30:00',
      deployedBy: '张三',
      deployEnvironment: '生产环境'
    },
    {
      id: 2,
      name: 'Agent-Dev-002',
      department: '发展改革委',
      server: '192.168.1.102',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 4,
      lastHeartbeat: '1分钟前',
      dataTypes: ['项目投资数据', '经济指标数据', '规划数据', '价格监测数据'],
      deployTime: '2024-01-20 14:15:00',
      deployedBy: '李四',
      deployEnvironment: '生产环境'
    },
    {
      id: 3,
      name: 'Agent-Edu-003',
      department: '教育局',
      server: '192.168.1.103',
      status: 'warning',
      version: 'v2.1.2',
      dataSources: 5,
      lastHeartbeat: '15分钟前',
      dataTypes: ['学生学籍数据', '教师信息数据', '学校基础数据', '考试成绩数据', '教育资源数据'],
      deployTime: '2023-12-28 16:45:00',
      deployedBy: '王五',
      deployEnvironment: '生产环境'
    },
    {
      id: 4,
      name: 'Agent-Tech-004',
      department: '科技局',
      server: '192.168.1.104',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 3,
      lastHeartbeat: '3分钟前',
      dataTypes: ['科技项目数据', '专利申请数据', '科技企业数据'],
      deployTime: '2024-02-01 10:20:00',
      deployedBy: '赵六',
      deployEnvironment: '生产环境'
    },
    {
      id: 5,
      name: 'Agent-Ind-005',
      department: '工信局',
      server: '192.168.1.105',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 4,
      lastHeartbeat: '1分钟前',
      dataTypes: ['工业企业数据', '产业园区数据', '信息化项目数据', '节能减排数据'],
      deployTime: '2024-01-25 11:00:00',
      deployedBy: '孙七',
      deployEnvironment: '生产环境'
    },
    {
      id: 6,
      name: 'Agent-Pub-006',
      department: '公安局',
      server: '192.168.1.106',
      status: 'online',
      version: 'v2.1.3',
      dataSources: 6,
      lastHeartbeat: '实时',
      dataTypes: ['人口基础信息', '户籍管理数据', '身份证数据', '治安管理数据', '交通违法数据', '案件管理数据'],
      deployTime: '2024-01-10 08:45:00',
      deployedBy: '周八',
      deployEnvironment: '生产环境'
    },
    {
      id: 7,
      name: 'Agent-Civ-007',
      department: '民政局',
      server: '*************',
      status: 'offline',
      version: 'v2.1.1',
      dataSources: 4,
      lastHeartbeat: '2小时前',
      dataTypes: ['婚姻登记数据', '社会救助数据', '养老服务数据', '社会组织数据'],
      deployTime: '2023-11-30 15:30:00',
      deployedBy: '吴九',
      deployEnvironment: '生产环境'
    }
  ]

  // 导航菜单项
  const menuItems = [
    {
      id: 'datasources',
      name: '数据源连接',
      icon: Database,
      description: '数据来源单位连接状态'
    },
    {
      id: 'agents',
      name: 'Agent状态',
      icon: Server,
      description: '采集Agent运行监控'
    },
    {
      id: 'monitoring',
      name: '实时监控',
      icon: Activity,
      description: '系统运行状态监控'
    },
    {
      id: 'settings',
      name: '系统配置',
      icon: Settings,
      description: '采集系统参数配置'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'offline': return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'warning': return <Clock className="w-4 h-4 text-yellow-500" />
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-700 border-green-200'
      case 'offline': return 'bg-red-100 text-red-700 border-red-200'
      case 'warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100">
      {/* 系统头部 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
        <div className="max-w mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* 系统Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <Database className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent">
                    🔄 数据采集系统
                  </h1>
                  <p className="text-sm text-gray-600">24个数据来源单位的数据采集情况实时监控</p>
                </div>
              </div>
            </div>

            {/* 导航菜单 */}
            <div className="hidden lg:flex items-center space-x-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = activeTab === item.id
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-white/80 text-purple-600 shadow-sm'
                        : 'text-gray-600 hover:text-purple-600 hover:bg-white/40'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="font-medium text-sm">{item.name}</span>
                  </button>
                )
              })}
            </div>

            {/* 返回主平台 */}
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-gray-600 hover:text-purple-600 transition-colors text-sm font-medium"
              >
                返回首页
              </a>
              <a
                href="/dashboard"
                className="bg-purple-600 text-white px-4 py-2 rounded-xl hover:bg-purple-700 transition-colors text-sm font-medium"
              >
                主控台
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 系统状态概览 */}
      <div className="bg-white/60 backdrop-blur-sm border-b border-white/20">
        <div className="max-w mx-auto px-6 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <h3 className="text-sm font-semibold text-gray-900">系统状态概览</h3>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-gray-600">Agent在线: {agentStats.onlineAgents}/{agentStats.totalAgents}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <HardDrive className="w-4 h-4 text-blue-500" />
                  <span className="text-gray-600">数据量: {agentStats.totalDataVolume}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Wifi className="w-4 h-4 text-green-500" />
                  <span className="text-gray-600">数据源: {agentStats.activeDataSources}/{agentStats.totalDataSources}</span>
                </div>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              最后更新: {agentStats.lastUpdate}
            </div>
          </div>
        </div>
      </div>

      {/* 菜单内容区域 */}
      <MenuContent
        activeTab={activeTab}
        agentStats={agentStats}
        agentDetails={agentDetails}
        getStatusIcon={getStatusIcon}
        getStatusColor={getStatusColor}
      />

      {/* 主要内容区域 */}
      <main className="relative z-10">
        {activeTab === 'datasources' ? children : (
          <div className="px-6 py-8">
            <div className="text-center text-gray-600">
              <p>请在上方菜单中选择要查看的内容</p>
            </div>
          </div>
        )}
      </main>

      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
    </div>
  )
}
